import { suite, test } from "@testdeck/mocha";
import { expect } from "chai";
import { ExtraMessageImpl, MrchExtraDataImpl } from "../skywind/actionableResponse";

@suite
class ActionableResponseSpec {
    @test
    public async createExtraData() {
        const message = ExtraMessageImpl.create()
            .hidePopupButtons()
            .setMessageType(2)
            .setMessage("Message without title");
        const extraData = MrchExtraDataImpl.create().addExtraMessage(message);

        expect(extraData).to.be.deep.equal({
            messageArray: [
                {
                    buttons: false,
                    message: "Message without title",
                    msgType: 2,
                    translate: false
                }
            ]
        });
    }
}
