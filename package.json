{"name": "sw-integration-seamless", "version": "5.55.0", "private": true, "description": "IPM protocol version 2", "main": "app.js", "scripts": {"clean": "rm -rf ./out", "compile": "tsc -b tsconfig.json", "start": "node out/skywind/app.js", "test": "nyc node_modules/mocha/bin/_mocha out/test/**/**/*.spec.js out/test/**/*.spec.js", "lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "version": "echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./out/skywind/version", "dev": "npm run compile && npm run version && INTERNAL_SERVER_PORT=9921 node out/skywind/app.js", "preinstall": "node preinstall.cjs"}, "repository": {"type": "git", "url": "ssh://******************************:7999/swb/sw-integration-seamless.git"}, "keywords": ["seamless", "ipm"], "author": "", "license": "ISC", "dependencies": {"@skywind-group/sw-adapter-regulation-support": "1.0.2", "@skywind-group/sw-currency-exchange": "2.3.17", "@skywind-group/sw-utils": "2.4.2", "@skywind-group/sw-wallet-adapter-core": "2.1.7", "@skywind-group/gelf-stream": "1.2.6", "body-parser": "1.20.3", "bole": "5.0.19", "compression": "1.8.0", "cookie-parser": "1.4.7", "agentkeepalive": "4.6.0", "express": "4.21.2", "express-validator": "7.2.1", "jsonwebtoken": "9.0.2", "inversify": "6.2.2", "inversify-express-utils": "6.4.10", "method-override": "3.0.0", "node-cache": "5.1.2", "node-schedule": "2.1.1", "fast-xml-parser": "4.4.1", "hashids": "2.3.0", "emitter-listener": "1.1.2", "express-prom-bundle": "7.0.2", "prom-client": "15.0.0", "generic-pool": "3.9.0", "ioredis": "5.6.1", "kafka-node": "5.0.0", "kafkajs": "2.2.4", "properties": "1.2.1", "reflect-metadata": "0.2.2", "superagent": "9.0.2", "superagent-proxy": "3.0.0"}, "devDependencies": {"@types/chai": "^4.3.20", "@types/chai-as-promised": "^7.1.8", "@types/express": "4.17.23", "@types/node": "^22.15.30", "@types/sinon": "^17.0.3", "bole-console": "0.1.10", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "eslint": "^9.22.0", "dotenv": "^16.0.3", "mocha": "^11.1.0", "mocha-typescript": "^1.1.17", "sinon": "^18.0.1", "superagent-mocker": "^0.5.2", "supertest": "^7.0.0", "ts-node": "^10.7.0", "typescript": "5.8.2", "nyc": "17.1.0"}, "packageManager": "pnpm@10.11.0"}